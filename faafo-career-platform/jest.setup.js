require('@testing-library/jest-dom');

// Mock Next.js server components
global.Request = global.Request || class Request {};
global.Response = global.Response || class Response {};
global.Headers = global.Headers || class Headers {};

// Mock Node.js APIs that are not available in Jest environment
global.TextEncoder = global.TextEncoder || require('util').TextEncoder;
global.TextDecoder = global.TextDecoder || require('util').TextDecoder;

// Mock Response.json for Jest environment
if (!global.Response || !global.Response.json) {
  global.Response = class MockResponse {
    constructor(body, init = {}) {
      this.body = body;
      this.status = init.status || 200;
      this.statusText = init.statusText || 'OK';
      this.headers = new Headers(init.headers);
      this.ok = this.status >= 200 && this.status < 300;
    }

    static json(data, init = {}) {
      return new MockResponse(JSON.stringify(data), {
        ...init,
        headers: {
          'Content-Type': 'application/json',
          ...init.headers
        }
      });
    }

    async json() {
      return JSON.parse(this.body);
    }

    async text() {
      return this.body;
    }
  };
}

// Mock browser APIs
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock environment variables
process.env.NODE_ENV = 'test';
process.env.NEXTAUTH_SECRET = 'test-secret';
process.env.NEXTAUTH_URL = 'http://localhost:3000';
process.env.DATABASE_URL = 'file:./test.db';

// Mock fetch globally
global.fetch = jest.fn();

// Mock NextAuth
jest.mock('next-auth/react', () => ({
  useSession: () => ({
    data: null,
    status: 'unauthenticated',
  }),
  signIn: jest.fn(),
  signOut: jest.fn(),
  SessionProvider: ({ children }) => children,
}));

// Mock next-themes
jest.mock('next-themes', () => ({
  useTheme: () => ({
    theme: 'light',
    setTheme: jest.fn(),
  }),
  ThemeProvider: ({ children }) => children,
}));

// Note: Prisma client mock is handled in jest.config.js moduleNameMapper

// Mock Prisma lib instead of the client directly
jest.mock('@/lib/prisma', () => ({
  __esModule: true,
  default: {
    user: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    assessment: {
      findFirst: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      count: jest.fn(),
      findUnique: jest.fn(),
    },
    assessmentResponse: {
      findMany: jest.fn(),
      create: jest.fn(),
      createMany: jest.fn(),
      deleteMany: jest.fn(),
      count: jest.fn(),
    },
    learningResource: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      count: jest.fn(),
    },
    careerPath: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      count: jest.fn(),
    },
    forumPost: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      count: jest.fn(),
    },
    forumReply: {
      findMany: jest.fn(),
      create: jest.fn(),
      count: jest.fn(),
    },
    forumPostReaction: {
      findFirst: jest.fn(),
      create: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    forumReplyReaction: {
      findFirst: jest.fn(),
      create: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    forumBookmark: {
      findFirst: jest.fn(),
      create: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    forumCategory: {
      findMany: jest.fn(),
      count: jest.fn(),
    },
    userGoal: {
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      count: jest.fn(),
    },
    achievement: {
      findMany: jest.fn(),
      count: jest.fn(),
    },
    userAchievement: {
      findMany: jest.fn(),
      create: jest.fn(),
      count: jest.fn(),
    },
    userProgress: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    resourceRating: {
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      count: jest.fn(),
    },
    profile: {
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      count: jest.fn(),
    },
    $disconnect: jest.fn(),
    $executeRawUnsafe: jest.fn(),
    $transaction: jest.fn(async (callback) => {
      // Mock transaction by calling the callback with a mock transaction client
      const mockAssessment = {
        id: 'test-assessment-' + Date.now(),
        userId: 'test-user-id',
        currentStep: 1,
        status: 'IN_PROGRESS',
        createdAt: new Date(),
        updatedAt: new Date(),
        completedAt: null,
      };

      const mockTx = {
        user: {
          findUnique: jest.fn(),
          findMany: jest.fn(),
          create: jest.fn(),
          update: jest.fn(),
          delete: jest.fn(),
          count: jest.fn(),
        },
        assessment: {
          findFirst: jest.fn().mockResolvedValue(null), // Default: no existing assessment
          findMany: jest.fn(),
          create: jest.fn().mockResolvedValue(mockAssessment),
          update: jest.fn().mockResolvedValue(mockAssessment),
          count: jest.fn(),
          findUnique: jest.fn().mockResolvedValue(mockAssessment),
        },
        assessmentResponse: {
          findMany: jest.fn(),
          create: jest.fn(),
          createMany: jest.fn().mockResolvedValue({ count: 0 }),
          deleteMany: jest.fn().mockResolvedValue({ count: 0 }),
          count: jest.fn(),
        },
      };
      return await callback(mockTx);
    }),
  },
}));

// Reset all mocks before each test
beforeEach(() => {
  jest.clearAllMocks();
});

// Mock NextResponse
jest.mock('next/server', () => ({
  NextResponse: {
    json: (data, init = {}) => {
      const response = {
        status: init.status || 200,
        headers: new Headers(init.headers),
        body: JSON.stringify(data),
        json: () => Promise.resolve(data),
        text: () => Promise.resolve(JSON.stringify(data)),
        ok: (init.status || 200) >= 200 && (init.status || 200) < 300,
      };
      return response;
    },
    next: (init = {}) => {
      return {
        status: init.status || 200,
        headers: new Headers(init.headers),
        body: null,
        json: () => Promise.resolve(null),
        text: () => Promise.resolve(''),
        ok: (init.status || 200) >= 200 && (init.status || 200) < 300,
      };
    },
    redirect: (url, init = {}) => {
      return {
        status: init.status || 302,
        headers: new Headers({ Location: url, ...init.headers }),
        body: null,
        json: () => Promise.resolve(null),
        text: () => Promise.resolve(''),
        ok: false,
      };
    },
  },
  NextRequest: jest.fn().mockImplementation((url, init = {}) => ({
    url,
    method: init.method || 'GET',
    headers: new Headers(init.headers),
    body: init.body,
    json: () => {
      try {
        return Promise.resolve(init.body ? JSON.parse(init.body) : {});
      } catch (error) {
        return Promise.reject(new SyntaxError('Unexpected token in JSON'));
      }
    },
    text: () => Promise.resolve(init.body || ''),
    formData: () => Promise.resolve(new FormData()),
    cookies: {
      get: jest.fn(),
      set: jest.fn(),
      delete: jest.fn(),
      has: jest.fn(),
      clear: jest.fn(),
    },
    nextUrl: {
      pathname: new URL(url).pathname,
      searchParams: new URL(url).searchParams,
    },
    geo: {},
    ip: '127.0.0.1',
  })),
}));

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    prefetch: jest.fn(),
  }),
  useSearchParams: () => ({
    get: jest.fn(),
    has: jest.fn(),
    getAll: jest.fn(),
    keys: jest.fn(),
    values: jest.fn(),
    entries: jest.fn(),
    forEach: jest.fn(),
    toString: jest.fn(),
  }),
  usePathname: () => '/',
}));

// Mock next-auth
jest.mock('next-auth/next', () => ({
  getServerSession: jest.fn(),
}));

// Mock Prisma enums
jest.mock('@prisma/client', () => ({
  AssessmentStatus: {
    IN_PROGRESS: 'IN_PROGRESS',
    COMPLETED: 'COMPLETED',
  },
  Prisma: {
    JsonNull: null,
  },
}));

jest.mock('next-auth/react', () => ({
  useSession: jest.fn(() => ({
    data: null,
    status: 'unauthenticated',
  })),
  SessionProvider: ({ children }) => children,
  signIn: jest.fn(),
  signOut: jest.fn(),
}));

// Suppress console warnings in tests
const originalWarn = console.warn;
console.warn = (...args) => {
  if (
    typeof args[0] === 'string' &&
    args[0].includes('Warning: ReactDOM.render is no longer supported')
  ) {
    return;
  }
  originalWarn.call(console, ...args);
};