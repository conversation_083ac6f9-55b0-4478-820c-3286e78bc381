const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testConnection() {
  try {
    console.log('🔍 Testing database connection...');
    
    // Test basic connection
    await prisma.$connect();
    console.log('✅ Database connected successfully!');
    
    // Test a simple query
    const result = await prisma.$queryRaw`SELECT 1 as test`;
    console.log('✅ Query test successful:', result);
    
    // Test creating a user
    console.log('\n🧪 Testing user creation...');
    const testUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: 'hashedpassword123',
        name: 'Test User'
      }
    });
    console.log('✅ Test user created:', testUser.email);
    
    // Test reading the user
    const foundUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });
    console.log('✅ Test user found:', foundUser?.email);
    
    // Clean up
    await prisma.user.delete({
      where: { email: '<EMAIL>' }
    });
    console.log('✅ Test user cleaned up');
    
    console.log('\n🎉 All database tests passed! Your Vercel Postgres migration is successful!');
    
  } catch (error) {
    console.error('❌ Database test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testConnection();
