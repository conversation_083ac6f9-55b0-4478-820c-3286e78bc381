// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id            String       @id @default(cuid())
  name          String? // Added for NextAuth
  email         String       @unique
  emailVerified DateTime? // Added for NextAuth
  image         String? // Added for NextAuth
  password      String
  passwordResetToken String? @unique
  passwordResetExpires DateTime?
  failedLoginAttempts Int @default(0)
  lockedUntil   DateTime?
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt
  profile       Profile? // One-to-one relationship with Profile
  assessments   Assessment[] // One-to-many relationship with Assessment
  forumPosts    ForumPost[] // One-to-many relationship with ForumPost
  forumReplies  ForumReply[] // One-to-many relationship with ForumReply
  freedomFund   FreedomFund? // One-to-one relationship with FreedomFund
  learningProgress UserLearningProgress[] // One-to-many relationship with UserLearningProgress
  resourceRatings  ResourceRating[] // One-to-many relationship with ResourceRating

  // Forum-related relationships
  forumPostReactions ForumPostReaction[]
  forumReplyReactions ForumReplyReaction[]
  forumBookmarks ForumBookmark[]
  forumReports ForumReport[]
  moderatorRoles ForumModerator[]

  // Progress tracking relationships
  goals UserGoal[]
  achievements UserAchievement[]

  // Advanced learning relationships
  learningPaths UserLearningPath[]
  learningPathProgress UserLearningPathProgress[]
  skillProgress UserSkillProgress[]
  learningAnalytics LearningAnalytics[]

  // Added for NextAuth
  accounts Account[]
  sessions Session[]
}

model Profile {
  id                String  @id @default(uuid())
  userId            String  @unique // One-to-one relationship with User
  user              User    @relation(fields: [userId], references: [id])
  bio               String?
  profilePictureUrl String?
  socialMediaLinks  Json?

  // Forum-specific profile fields
  forumSignature    String?
  forumBio          String?
  forumReputation   Int     @default(0)
  forumPostCount    Int     @default(0)
  forumReplyCount   Int     @default(0)
  joinedAt          DateTime @default(now())
  lastActiveAt      DateTime @default(now())

  // Career-related display fields for forum
  currentCareerPath String?
  progressLevel     String?
  achievements      Json?   // Store user achievements/badges
}

enum AssessmentStatus {
  IN_PROGRESS
  COMPLETED
}

model Assessment {
  id          String               @id @default(uuid())
  userId      String
  user        User                 @relation(fields: [userId], references: [id])
  status      AssessmentStatus     @default(IN_PROGRESS)
  currentStep Int                  @default(0)
  createdAt   DateTime             @default(now())
  updatedAt   DateTime             @updatedAt
  completedAt DateTime?
  responses   AssessmentResponse[]
}

model AssessmentResponse {
  id           String     @id @default(uuid())
  assessmentId String
  assessment   Assessment @relation(fields: [assessmentId], references: [id], onDelete: Cascade)
  questionKey  String // e.g., "dissatisfaction_triggers", "desired_outcomes_skill_a"
  answerValue  Json // Flexible to store various answer types (string, array, number)
  createdAt    DateTime   @default(now())

  @@index([assessmentId])
}

model CareerPath {
  id                String           @id @default(uuid())
  name              String           @unique
  slug              String           @unique // For URL-friendly identifiers
  overview          String
  pros              String // JSON string of advantages
  cons              String // JSON string of disadvantages
  actionableSteps   Json // e.g., [{ title: "Step 1", description: "..."}]
  isActive          Boolean          @default(true)
  relatedSkills     Skill[]          @relation("CareerPathToSkill")
  relatedIndustries Industry[]       @relation("CareerPathToIndustry")
  suggestionRules   SuggestionRule[]
  learningResources LearningResource[] @relation("CareerPathToLearningResource")
  learningPaths     LearningPath[] @relation("CareerPathToLearningPath")
  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt
}

model Skill {
  id                String             @id @default(uuid())
  name              String             @unique
  description       String?
  category          String? // e.g., "Technical", "Soft Skills", "Industry-Specific"
  careerPaths       CareerPath[]       @relation("CareerPathToSkill")
  learningResources LearningResource[] @relation("SkillToLearningResource")
  learningPaths     LearningPath[] @relation("LearningPathToSkill")
  userProgress      UserSkillProgress[]
  createdAt         DateTime           @default(now())
  updatedAt         DateTime           @updatedAt
}

model Industry {
  id          String       @id @default(uuid())
  name        String       @unique
  careerPaths CareerPath[] @relation("CareerPathToIndustry")
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
}

model SuggestionRule {
  id           String     @id @default(uuid())
  careerPathId String
  careerPath   CareerPath @relation(fields: [careerPathId], references: [id])
  questionKey  String // Corresponds to AssessmentResponse.questionKey
  answerValue  Json // The specific answer value or pattern that triggers this rule
  weight       Float      @default(1.0) // How much this rule contributes
  notes        String?
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt

  @@index([careerPathId])
}

model ForumPost {
  id        String       @id @default(uuid())
  title     String
  content   String
  authorId  String
  author    User         @relation(fields: [authorId], references: [id])
  categoryId String?
  category  ForumCategory? @relation(fields: [categoryId], references: [id])
  tags      Json?        // JSON array of tags for better searchability
  replies   ForumReply[] // One-to-many relationship with ForumReply
  reactions ForumPostReaction[]
  bookmarks ForumBookmark[]
  reports   ForumReport[]

  // Engagement metrics
  viewCount Int @default(0)
  likeCount Int @default(0)
  replyCount Int @default(0)

  // Moderation fields
  isLocked  Boolean @default(false)
  isHidden  Boolean @default(false)
  isPinned  Boolean @default(false)
  moderatedBy String?
  moderatedAt DateTime?
  moderationReason String?

  createdAt DateTime     @default(now())
  updatedAt DateTime     @updatedAt
}

model ForumReply {
  id        String    @id @default(uuid())
  content   String
  authorId  String
  author    User      @relation(fields: [authorId], references: [id])
  postId    String
  post      ForumPost @relation(fields: [postId], references: [id])
  reactions ForumReplyReaction[]
  reports   ForumReport[]

  // Engagement metrics
  likeCount Int @default(0)

  // Moderation fields
  isHidden  Boolean @default(false)
  moderatedBy String?
  moderatedAt DateTime?
  moderationReason String?

  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
}

model FreedomFund {
  id                   String   @id @default(uuid())
  userId               String   @unique
  user                 User     @relation(fields: [userId], references: [id])
  monthlyExpenses      Float
  coverageMonths       Int
  targetSavings        Float
  currentSavingsAmount Float? // Optional, as user might not have started saving
  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt // Automatically updated on save
}

// Added for NextAuth
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

// Added for NextAuth
model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

// Added for NextAuth
model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// New models for Learning Resources
model LearningResource {
  id                String                    @id @default(uuid())
  title             String
  description       String
  url               String                    @unique
  type              LearningResourceType
  category          LearningResourceCategory
  skillLevel        SkillLevel
  author            String?
  duration          String?
  cost              LearningResourceCost      @default(FREE)
  format            LearningResourceFormat
  isActive          Boolean                   @default(true)
  careerPaths       CareerPath[]              @relation("CareerPathToLearningResource")
  skills            Skill[]                   @relation("SkillToLearningResource")
  userProgress      UserLearningProgress[]
  ratings           ResourceRating[]
  learningPathSteps LearningPathStep[]
  createdAt         DateTime                  @default(now())
  updatedAt         DateTime                  @updatedAt
}

model UserLearningProgress {
  id                String           @id @default(uuid())
  userId            String
  user              User             @relation(fields: [userId], references: [id])
  resourceId        String
  resource          LearningResource @relation(fields: [resourceId], references: [id])
  status            ProgressStatus   @default(NOT_STARTED)
  completedAt       DateTime?
  notes             String?
  rating            Int?             // 1-5 star rating
  review            String?          // User review text
  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt

  @@unique([userId, resourceId])
}

model ResourceRating {
  id                String           @id @default(uuid())
  userId            String
  user              User             @relation(fields: [userId], references: [id])
  resourceId        String
  resource          LearningResource @relation(fields: [resourceId], references: [id])
  rating            Int              // 1-5 star rating
  review            String?          // Optional review text
  isHelpful         Boolean?         // Whether user found resource helpful
  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt

  @@unique([userId, resourceId])
}

enum LearningResourceType {
  COURSE
  ARTICLE
  VIDEO
  PODCAST
  BOOK
  CERTIFICATION
  TUTORIAL
  WORKSHOP
}

enum LearningResourceCategory {
  CYBERSECURITY
  DATA_SCIENCE
  BLOCKCHAIN
  PROJECT_MANAGEMENT
  DIGITAL_MARKETING
  FINANCIAL_LITERACY
  LANGUAGE_LEARNING
  ARTIFICIAL_INTELLIGENCE
  WEB_DEVELOPMENT
  MOBILE_DEVELOPMENT
  CLOUD_COMPUTING
  ENTREPRENEURSHIP
  UX_UI_DESIGN
  PRODUCT_MANAGEMENT
  DEVOPS
}

enum SkillLevel {
  BEGINNER
  INTERMEDIATE
  ADVANCED
  EXPERT
}

enum LearningResourceCost {
  FREE
  FREEMIUM
  PAID
  SUBSCRIPTION
}

enum LearningResourceFormat {
  SELF_PACED
  INSTRUCTOR_LED
  INTERACTIVE
  HANDS_ON
  THEORETICAL
}

enum ProgressStatus {
  NOT_STARTED
  IN_PROGRESS
  COMPLETED
  BOOKMARKED
}

// Forum Category Models
model ForumCategory {
  id          String @id @default(uuid())
  name        String @unique
  slug        String @unique
  description String?
  guidelines  String?
  parentId    String?
  parent      ForumCategory? @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children    ForumCategory[] @relation("CategoryHierarchy")
  posts       ForumPost[]
  moderators  ForumModerator[]

  // Display settings
  icon        String?
  color       String?
  sortOrder   Int @default(0)
  isActive    Boolean @default(true)

  // Statistics
  postCount   Int @default(0)
  replyCount  Int @default(0)
  lastPostAt  DateTime?
  lastPostBy  String?

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

// Forum Reaction Models
model ForumPostReaction {
  id       String @id @default(uuid())
  userId   String
  user     User   @relation(fields: [userId], references: [id])
  postId   String
  post     ForumPost @relation(fields: [postId], references: [id], onDelete: Cascade)
  type     ReactionType
  createdAt DateTime @default(now())

  @@unique([userId, postId])
}

model ForumReplyReaction {
  id       String @id @default(uuid())
  userId   String
  user     User   @relation(fields: [userId], references: [id])
  replyId  String
  reply    ForumReply @relation(fields: [replyId], references: [id], onDelete: Cascade)
  type     ReactionType
  createdAt DateTime @default(now())

  @@unique([userId, replyId])
}

enum ReactionType {
  LIKE
  HELPFUL
  INSIGHTFUL
  FUNNY
  LOVE
  DISAGREE
}

// Forum Bookmark Model
model ForumBookmark {
  id       String @id @default(uuid())
  userId   String
  user     User   @relation(fields: [userId], references: [id])
  postId   String
  post     ForumPost @relation(fields: [postId], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now())

  @@unique([userId, postId])
}

// Forum Moderation Models
model ForumModerator {
  id         String @id @default(uuid())
  userId     String
  user       User   @relation(fields: [userId], references: [id])
  categoryId String?
  category   ForumCategory? @relation(fields: [categoryId], references: [id])
  role       ModeratorRole
  permissions Json // Flexible permissions object
  assignedBy String
  assignedAt DateTime @default(now())
  isActive   Boolean @default(true)

  @@index([userId])
  @@index([categoryId])
}

model ForumReport {
  id         String @id @default(uuid())
  reporterId String
  reporter   User   @relation(fields: [reporterId], references: [id])
  postId     String?
  post       ForumPost? @relation(fields: [postId], references: [id])
  replyId    String?
  reply      ForumReply? @relation(fields: [replyId], references: [id])
  reason     ReportReason
  description String?
  status     ReportStatus @default(PENDING)
  reviewedBy String?
  reviewedAt DateTime?
  resolution String?
  createdAt  DateTime @default(now())

  @@index([status])
  @@index([createdAt])
}

enum ModeratorRole {
  MODERATOR
  ADMIN
  SUPER_ADMIN
}

enum ReportReason {
  SPAM
  INAPPROPRIATE_CONTENT
  HARASSMENT
  OFF_TOPIC
  MISINFORMATION
  COPYRIGHT_VIOLATION
  OTHER
}

enum ReportStatus {
  PENDING
  UNDER_REVIEW
  RESOLVED
  DISMISSED
}

// Goal and Achievement Models
model UserGoal {
  id          String @id @default(uuid())
  userId      String
  user        User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  title       String
  description String?
  type        GoalType
  category    GoalCategory
  status      GoalStatus @default(ACTIVE)
  targetValue Int
  currentValue Int @default(0)
  targetDate  DateTime?
  isPublic    Boolean @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([userId])
  @@index([status])
}

model Achievement {
  id              String @id @default(uuid())
  title           String @unique
  description     String
  icon            String
  type            AchievementType
  criteria        Json // Flexible criteria for unlocking
  points          Int @default(0)
  isActive        Boolean @default(true)
  userAchievements UserAchievement[]
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
}

model UserAchievement {
  id            String @id @default(uuid())
  userId        String
  user          User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  achievementId String
  achievement   Achievement @relation(fields: [achievementId], references: [id], onDelete: Cascade)
  unlockedAt    DateTime @default(now())
  progress      Json? // Track progress towards achievement

  @@unique([userId, achievementId])
  @@index([userId])
}

enum GoalType {
  DAILY
  WEEKLY
  MONTHLY
  YEARLY
  CUSTOM
}

enum GoalCategory {
  LEARNING_RESOURCES
  SKILLS
  CERTIFICATIONS
  PROJECTS
  CAREER_MILESTONES
  NETWORKING
}

enum GoalStatus {
  ACTIVE
  COMPLETED
  PAUSED
  CANCELLED
}

enum AchievementType {
  LEARNING_MILESTONE
  STREAK_ACHIEVEMENT
  COMPLETION_BADGE
  COMMUNITY_CONTRIBUTOR
  SKILL_MASTER
  GOAL_ACHIEVER
}

// Advanced Learning Management Models
model LearningPath {
  id              String @id @default(uuid())
  title           String
  description     String
  slug            String @unique
  difficulty      SkillLevel
  estimatedHours  Int
  prerequisites   Json? // Array of prerequisite learning path IDs or skills
  isActive        Boolean @default(true)
  createdBy       String? // Admin or system created

  // Relationships
  steps           LearningPathStep[]
  userPaths       UserLearningPath[]
  careerPaths     CareerPath[] @relation("CareerPathToLearningPath")
  skills          Skill[] @relation("LearningPathToSkill")

  // Metadata
  tags            Json? // Array of tags for categorization
  imageUrl        String?
  category        LearningResourceCategory

  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
}

model LearningPathStep {
  id              String @id @default(uuid())
  learningPathId  String
  learningPath    LearningPath @relation(fields: [learningPathId], references: [id], onDelete: Cascade)

  title           String
  description     String
  stepOrder       Int
  stepType        LearningStepType
  estimatedMinutes Int

  // Content references
  resourceId      String?
  resource        LearningResource? @relation(fields: [resourceId], references: [id])
  externalUrl     String?
  content         Json? // Rich content for custom steps

  // Requirements
  isRequired      Boolean @default(true)
  prerequisites   Json? // Array of prerequisite step IDs

  // User progress
  userProgress    UserLearningPathProgress[]

  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@index([learningPathId, stepOrder])
}

model UserLearningPath {
  id              String @id @default(uuid())
  userId          String
  user            User @relation(fields: [userId], references: [id], onDelete: Cascade)
  learningPathId  String
  learningPath    LearningPath @relation(fields: [learningPathId], references: [id], onDelete: Cascade)

  status          LearningPathStatus @default(NOT_STARTED)
  startedAt       DateTime?
  completedAt     DateTime?
  lastAccessedAt  DateTime @default(now())

  // Progress tracking
  currentStepId   String?
  completedSteps  Int @default(0)
  totalSteps      Int @default(0)
  progressPercent Int @default(0)

  // Time tracking
  totalTimeSpent  Int @default(0) // in minutes

  // User notes and feedback
  notes           String?
  rating          Int? // 1-5 star rating
  review          String?

  // Step progress
  stepProgress    UserLearningPathProgress[]

  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@unique([userId, learningPathId])
  @@index([userId, status])
}

model UserLearningPathProgress {
  id              String @id @default(uuid())
  userId          String
  user            User @relation(fields: [userId], references: [id], onDelete: Cascade)
  userLearningPathId String
  userLearningPath UserLearningPath @relation(fields: [userLearningPathId], references: [id], onDelete: Cascade)
  stepId          String
  step            LearningPathStep @relation(fields: [stepId], references: [id], onDelete: Cascade)

  status          ProgressStatus @default(NOT_STARTED)
  startedAt       DateTime?
  completedAt     DateTime?
  timeSpent       Int @default(0) // in minutes

  // Step-specific data
  score           Int? // For quiz/assessment steps
  attempts        Int @default(0)
  notes           String?

  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@unique([userId, stepId])
  @@index([userId, status])
}

// Skill progression tracking
model UserSkillProgress {
  id              String @id @default(uuid())
  userId          String
  user            User @relation(fields: [userId], references: [id], onDelete: Cascade)
  skillId         String
  skill           Skill @relation(fields: [skillId], references: [id], onDelete: Cascade)

  currentLevel    SkillLevel @default(BEGINNER)
  progressPoints  Int @default(0)
  lastPracticed   DateTime?

  // Evidence of skill development
  completedResources Int @default(0)
  completedPaths  Int @default(0)
  practiceHours   Int @default(0)

  // Assessments and validations
  selfAssessment  Int? // 1-10 self-rating
  peerValidations Int @default(0)
  certifications  Json? // Array of certification info

  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@unique([userId, skillId])
  @@index([userId, currentLevel])
}

// Learning analytics and insights
model LearningAnalytics {
  id              String @id @default(uuid())
  userId          String
  user            User @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Time-based metrics
  date            DateTime @default(now())
  timeSpent       Int @default(0) // minutes spent learning
  resourcesViewed Int @default(0)
  pathsProgressed Int @default(0)
  skillsImproved  Int @default(0)

  // Engagement metrics
  loginStreak     Int @default(0)
  weeklyGoalMet   Boolean @default(false)
  monthlyGoalMet  Boolean @default(false)

  // Learning velocity
  avgCompletionTime Float? // average time to complete resources
  learningVelocity Float? // resources completed per week

  // Metadata
  deviceType      String?
  sessionDuration Int? // minutes

  @@unique([userId, date])
  @@index([userId, date])
}

enum LearningStepType {
  RESOURCE
  QUIZ
  ASSIGNMENT
  PROJECT
  DISCUSSION
  REFLECTION
  EXTERNAL_LINK
  VIDEO
  READING
  PRACTICE
}

enum LearningPathStatus {
  NOT_STARTED
  IN_PROGRESS
  COMPLETED
  PAUSED
  ARCHIVED
}
