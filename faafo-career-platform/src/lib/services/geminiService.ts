import { GoogleGenerativeAI, GenerativeModel, GenerationConfig } from '@google/generative-ai';
import { cache } from '@/lib/cache';

// Initialize Google Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GEMINI_API_KEY || '');

// Configuration for different AI tasks
const configs: Record<string, GenerationConfig> = {
  resume_analysis: {
    temperature: 0.3,
    topK: 40,
    topP: 0.95,
    maxOutputTokens: 2048,
  },
  career_recommendations: {
    temperature: 0.7,
    topK: 40,
    topP: 0.95,
    maxOutputTokens: 1024,
  },
  skills_analysis: {
    temperature: 0.4,
    topK: 40,
    topP: 0.95,
    maxOutputTokens: 1536,
  },
  interview_prep: {
    temperature: 0.6,
    topK: 40,
    topP: 0.95,
    maxOutputTokens: 2048,
  },
};

export interface AIResponse {
  success: boolean;
  data?: any;
  error?: string;
  cached?: boolean;
}

export interface ResumeAnalysisResult {
  strengths: string[];
  weaknesses: string[];
  suggestions: string[];
  skillsIdentified: string[];
  experienceLevel: 'entry' | 'mid' | 'senior' | 'executive';
  industryFit: string[];
  overallScore: number;
}

export interface CareerRecommendation {
  careerPath: string;
  matchScore: number;
  reasoning: string;
  requiredSkills: string[];
  timeToTransition: string;
  salaryRange: string;
  growthPotential: string;
}

export interface SkillsAnalysisResult {
  currentSkills: string[];
  skillGaps: string[];
  learningRecommendations: {
    skill: string;
    priority: 'high' | 'medium' | 'low';
    estimatedTime: string;
    resources: string[];
  }[];
  careerReadiness: number;
}

export interface InterviewPrepResult {
  commonQuestions: {
    question: string;
    category: 'behavioral' | 'technical' | 'situational';
    sampleAnswer: string;
    tips: string[];
  }[];
  industrySpecificQuestions: {
    question: string;
    difficulty: 'easy' | 'medium' | 'hard';
    keyPoints: string[];
  }[];
  preparationTips: string[];
}

class GeminiService {
  private model: GenerativeModel;
  private cacheEnabled: boolean;
  private cacheTTL: number;

  constructor() {
    this.model = genAI.getGenerativeModel({ model: 'gemini-pro' });
    this.cacheEnabled = process.env.NODE_ENV === 'production';
    this.cacheTTL = parseInt(process.env.AI_CACHE_TTL || '3600');
  }

  private async generateContent(
    prompt: string,
    configKey: string,
    cacheKey?: string
  ): Promise<AIResponse> {
    try {
      // Check cache first if enabled
      if (this.cacheEnabled && cacheKey) {
        const cached = await cache.get(cacheKey);
        if (cached && typeof cached === 'string') {
          return {
            success: true,
            data: JSON.parse(cached),
            cached: true,
          };
        }
      }

      // Configure the model for this specific task
      const config = configs[configKey] || configs.career_recommendations;
      
      const result = await this.model.generateContent({
        contents: [{ role: 'user', parts: [{ text: prompt }] }],
        generationConfig: config,
      });

      const response = await result.response;
      const text = response.text();

      // Try to parse as JSON, fallback to plain text
      let data;
      try {
        data = JSON.parse(text);
      } catch {
        data = { content: text };
      }

      // Cache the result if enabled
      if (this.cacheEnabled && cacheKey) {
        await cache.set(cacheKey, JSON.stringify(data), this.cacheTTL);
      }

      return {
        success: true,
        data,
        cached: false,
      };
    } catch (error) {
      console.error('Gemini AI Error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'AI service unavailable',
      };
    }
  }

  async analyzeResume(resumeText: string, userId?: string): Promise<AIResponse> {
    const prompt = `
Analyze the following resume and provide a comprehensive assessment in JSON format:

Resume Text:
${resumeText}

Please provide your analysis in the following JSON structure:
{
  "strengths": ["list of key strengths"],
  "weaknesses": ["areas for improvement"],
  "suggestions": ["specific actionable suggestions"],
  "skillsIdentified": ["list of skills found in resume"],
  "experienceLevel": "entry|mid|senior|executive",
  "industryFit": ["industries this person would fit well in"],
  "overallScore": number between 1-100
}

Focus on:
1. Professional presentation and formatting
2. Skills and experience relevance
3. Career progression and achievements
4. Areas for improvement
5. Industry alignment
`;

    const cacheKey = userId ? `resume_analysis:${userId}:${Buffer.from(resumeText).toString('base64').slice(0, 32)}` : undefined;
    
    return this.generateContent(prompt, 'resume_analysis', cacheKey);
  }

  async generateCareerRecommendations(
    assessmentData: any,
    currentSkills: string[],
    preferences: any,
    userId?: string
  ): Promise<AIResponse> {
    const prompt = `
Based on the following career assessment data, current skills, and preferences, provide personalized career path recommendations in JSON format:

Assessment Data:
${JSON.stringify(assessmentData, null, 2)}

Current Skills:
${currentSkills.join(', ')}

Preferences:
${JSON.stringify(preferences, null, 2)}

Please provide 3-5 career recommendations in the following JSON structure:
{
  "recommendations": [
    {
      "careerPath": "specific career title",
      "matchScore": number between 1-100,
      "reasoning": "detailed explanation of why this fits",
      "requiredSkills": ["skills needed for this career"],
      "timeToTransition": "estimated time to transition",
      "salaryRange": "typical salary range",
      "growthPotential": "career growth prospects"
    }
  ]
}

Consider:
1. Skills alignment and transferability
2. Personal values and work preferences
3. Market demand and growth potential
4. Realistic transition timeline
5. Salary expectations and growth
`;

    const cacheKey = userId ? `career_recommendations:${userId}:${Date.now().toString().slice(-8)}` : undefined;
    
    return this.generateContent(prompt, 'career_recommendations', cacheKey);
  }

  async analyzeSkillsGap(
    currentSkills: string[],
    targetCareerPath: string,
    experienceLevel: string,
    userId?: string
  ): Promise<AIResponse> {
    const prompt = `
Analyze the skills gap for transitioning to a specific career path and provide learning recommendations in JSON format:

Current Skills:
${currentSkills.join(', ')}

Target Career Path:
${targetCareerPath}

Experience Level:
${experienceLevel}

Please provide your analysis in the following JSON structure:
{
  "currentSkills": ["validated current skills relevant to target career"],
  "skillGaps": ["skills needed but not currently possessed"],
  "learningRecommendations": [
    {
      "skill": "specific skill name",
      "priority": "high|medium|low",
      "estimatedTime": "time to learn this skill",
      "resources": ["recommended learning resources or types"]
    }
  ],
  "careerReadiness": number between 1-100
}

Focus on:
1. Most critical skills for the target career
2. Realistic learning timelines
3. Prioritization based on impact
4. Practical learning approaches
5. Current market requirements
`;

    const cacheKey = userId ? `skills_analysis:${userId}:${targetCareerPath.replace(/\s+/g, '_')}` : undefined;
    
    return this.generateContent(prompt, 'skills_analysis', cacheKey);
  }

  async generateInterviewPrep(
    careerPath: string,
    experienceLevel: string,
    companyType: string,
    userId?: string
  ): Promise<AIResponse> {
    const prompt = `
Generate comprehensive interview preparation materials for the following scenario in JSON format:

Career Path: ${careerPath}
Experience Level: ${experienceLevel}
Company Type: ${companyType}

Please provide interview preparation in the following JSON structure:
{
  "commonQuestions": [
    {
      "question": "interview question",
      "category": "behavioral|technical|situational",
      "sampleAnswer": "example answer framework",
      "tips": ["specific tips for answering this question"]
    }
  ],
  "industrySpecificQuestions": [
    {
      "question": "industry-specific question",
      "difficulty": "easy|medium|hard",
      "keyPoints": ["key points to address in answer"]
    }
  ],
  "preparationTips": ["general interview preparation advice"]
}

Include:
1. 8-10 common interview questions with sample answers
2. 5-7 industry-specific technical questions
3. Behavioral questions using STAR method
4. Company research tips
5. Questions to ask the interviewer
`;

    const cacheKey = userId ? `interview_prep:${userId}:${careerPath.replace(/\s+/g, '_')}_${companyType}` : undefined;
    
    return this.generateContent(prompt, 'interview_prep', cacheKey);
  }

  async generatePersonalizedContent(
    contentType: string,
    userContext: any,
    additionalParams: any = {},
    userId?: string
  ): Promise<AIResponse> {
    const prompt = `
Generate personalized ${contentType} content based on the following user context:

User Context:
${JSON.stringify(userContext, null, 2)}

Additional Parameters:
${JSON.stringify(additionalParams, null, 2)}

Please provide relevant, actionable, and personalized content that helps the user with their career transition goals.
Format the response as JSON with appropriate structure for the content type.
`;

    const cacheKey = userId ? `personalized_content:${userId}:${contentType}:${Date.now().toString().slice(-8)}` : undefined;
    
    return this.generateContent(prompt, 'career_recommendations', cacheKey);
  }

  // Health check method
  async healthCheck(): Promise<boolean> {
    try {
      const result = await this.model.generateContent({
        contents: [{ role: 'user', parts: [{ text: 'Hello, please respond with "OK"' }] }],
        generationConfig: { maxOutputTokens: 10 },
      });
      
      const response = await result.response;
      return response.text().toLowerCase().includes('ok');
    } catch {
      return false;
    }
  }
}

export const geminiService = new GeminiService();
export default geminiService;
