/* eslint-disable security/detect-object-injection */
import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth'; // Import from the new shared file
import prisma from '@/lib/prisma'; // Assuming prisma client is at lib/prisma.ts
import { AssessmentStatus, Prisma } from '@prisma/client';
import { getQuestionByKey, Question as AssessmentQuestionConfig, getAllQuestions } from '@/lib/assessmentDefinition'; // Import shared definition

interface AssessmentSaveRequest {
  currentStep: number;
  formData: { [key: string]: Prisma.JsonValue | null };
  status?: 'IN_PROGRESS' | 'COMPLETED'; // Optional: client might send status for final submission
}

interface AssessmentSubmitRequest {
  assessmentId: string;
  formData: { [key: string]: Prisma.JsonValue | null };
}

const validateFormData = (formData: { [key: string]: Prisma.JsonValue | null }): { sanitizedData: { [key: string]: Prisma.JsonValue | null }, error: string | null } => {
  const validKeys = getAllQuestions().map(q => q.key);
  const sanitizedData: { [key: string]: Prisma.JsonValue | null } = {};

  for (const key of Object.keys(formData)) {
    if (!validKeys.includes(key)) {
      return { sanitizedData: {}, error: `Invalid question key submitted: ${key}.` };
    }
    const questionConfig = getQuestionByKey(key) as AssessmentQuestionConfig | undefined;
    if (!questionConfig) {
      return { sanitizedData: {}, error: `Invalid question key submitted: ${key}.` };
    }
    const value = formData[key];

    // Check required fields
    if (questionConfig.required) {
      if (value === null || value === undefined || (Array.isArray(value) && value.length === 0) || String(value).trim() === '') {
        return { sanitizedData: {}, error: `Missing required answer for question: ${questionConfig.text} (key: ${key}).` };
      }
    }

    // Validate data types and values based on question type
    if (value !== null && value !== undefined) {
      if (questionConfig.type === 'multipleChoice') {
        const mcQuestion = questionConfig as any;
        if (mcQuestion.allowMultiple) {
          // Should be an array
          if (!Array.isArray(value)) {
            return { sanitizedData: {}, error: `Question '${key}' expects an array of values, but received: ${typeof value}.` };
          }
          // Check if all values are valid options
          if (mcQuestion.options) {
            const validOptions = mcQuestion.options.map((opt: any) => opt.value);
            for (const val of value) {
              if (!validOptions.includes(val)) {
                return { sanitizedData: {}, error: `Invalid option '${val}' for question '${key}'. Valid options: ${validOptions.join(', ')}.` };
              }
            }
          }
        } else {
          // Should be a single string value
          if (typeof value !== 'string') {
            return { sanitizedData: {}, error: `Question '${key}' expects a string value, but received: ${typeof value}.` };
          }
          // Check if value is a valid option
          if (mcQuestion.options) {
            const validOptions = mcQuestion.options.map((opt: any) => opt.value);
            if (!validOptions.includes(value)) {
              return { sanitizedData: {}, error: `Invalid option '${value}' for question '${key}'. Valid options: ${validOptions.join(', ')}.` };
            }
          }
        }
      } else if (questionConfig.type === 'scale') {
        // Should be a number
        if (typeof value !== 'number') {
          return { sanitizedData: {}, error: `Question '${key}' expects a number value, but received: ${typeof value}.` };
        }
        const scQuestion = questionConfig as any;
        if (scQuestion.numberOfSteps && (value < 1 || value > scQuestion.numberOfSteps)) {
          return { sanitizedData: {}, error: `Question '${key}' expects a value between 1 and ${scQuestion.numberOfSteps}, but received: ${value}.` };
        }
      } else if (questionConfig.type === 'text') {
        // Should be a string
        if (typeof value !== 'string') {
          return { sanitizedData: {}, error: `Question '${key}' expects a string value, but received: ${typeof value}.` };
        }
        const textQuestion = questionConfig as any;
        if (textQuestion.maxLength && value.length > textQuestion.maxLength) {
          return { sanitizedData: {}, error: `Question '${key}' text is too long. Maximum length: ${textQuestion.maxLength}.` };
        }
        if (textQuestion.minLength && value.length < textQuestion.minLength) {
          return { sanitizedData: {}, error: `Question '${key}' text is too short. Minimum length: ${textQuestion.minLength}.` };
        }
      }
    }

    sanitizedData[key] = value; // Only assign if key is valid
  }
  return { sanitizedData, error: null }; // Return sanitized data and no error
};

// GET handler to retrieve current user's active assessment
export async function GET() {
  const session = await getServerSession(authOptions);

  if (!session || !session.user || !session.user.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const assessment = await prisma.assessment.findFirst({
      where: {
        userId: session.user.id,
        status: 'IN_PROGRESS', // Only look for in-progress assessments for the assessment taking flow
      },
      include: {
        responses: true, // Include all responses associated with this assessment
      },
      orderBy: {
        updatedAt: 'desc', // Get the most recently updated in-progress assessment if multiple somehow exist
      },
    });

    if (!assessment) {
      return NextResponse.json({ message: 'No active assessment found for this user.' }, { status: 404 });
    }

    // Transform responses into a more convenient formData-like structure if needed by client
    // For now, returning the raw structure
    const formData: { [key: string]: Prisma.JsonValue | null } = {};
    assessment.responses.forEach(response => {
      formData[response.questionKey] = response.answerValue;
    });

    return NextResponse.json({
      currentStep: assessment.currentStep,
      formData: formData,
      status: assessment.status,
      updatedAt: assessment.updatedAt,
      id: assessment.id,
    });

  } catch (error) {
    console.error('Error fetching assessment:', error);
    return NextResponse.json({ error: 'Failed to fetch assessment data.' }, { status: 500 });
  }
}

// POST handler to save/update assessment progress
export async function POST(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session || !session.user || !session.user.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const body = await request.json() as AssessmentSaveRequest;
    const { currentStep, formData, status: newStatus } = body;

    if (typeof currentStep !== 'number' || !formData) {
      return NextResponse.json({ error: 'Invalid request body: currentStep and formData are required.' }, { status: 400 });
    }

    const { sanitizedData, error } = validateFormData(formData);
    if (error) {
      return NextResponse.json({ error: error }, { status: 400 });
    }

    const userId = session.user.id;

    // Upsert logic for Assessment and AssessmentResponses
    const result = await prisma.$transaction(async (tx) => {
      let assessment = await tx.assessment.findFirst({
        where: {
          userId: userId,
          // If a COMPLETED assessment exists, user might want to start a new one.
          // For now, we assume only one IN_PROGRESS or find the latest if multiple exist.
          // This logic might need refinement based on product decisions (e.g., allow multiple assessments).
          status: 'IN_PROGRESS',
        },
        orderBy: {
          createdAt: 'desc' // Get the latest IN_PROGRESS one
        }
      });

      if (!assessment) {
        // If no IN_PROGRESS assessment, create a new one
        assessment = await tx.assessment.create({
          data: {
            userId: userId,
            currentStep: currentStep,
            status: newStatus === 'COMPLETED' ? AssessmentStatus.COMPLETED : AssessmentStatus.IN_PROGRESS,
            // completedAt will be set by the PUT request for final submit
          },
        });
      } else {
        // Update existing assessment
        assessment = await tx.assessment.update({
          where: { id: assessment.id },
          data: {
            currentStep: currentStep,
            status: newStatus === 'COMPLETED' ? AssessmentStatus.COMPLETED : assessment.status, // Don't revert COMPLETED to IN_PROGRESS via POST
            updatedAt: new Date(),
          },
        });
      }

      // Process responses: delete existing for this assessment and recreate all based on formData
      // This is simpler than trying to diff and update individual responses.
      // For high-frequency saves, a more granular update might be better, but this is robust.
      await tx.assessmentResponse.deleteMany({
        where: { assessmentId: assessment.id },
      });

      const responseCreateData = Object.entries(sanitizedData).map(([key, value]) => ({
        assessmentId: assessment.id,
        questionKey: key,
        // Use Prisma.JsonNull for null values, otherwise pass the value directly.
        // The `value` can be string, number, array, or other JSON-compatible structures.
        answerValue: value === null || value === undefined ? Prisma.JsonNull : value,
      }));

      if (responseCreateData.length > 0) {
        await tx.assessmentResponse.createMany({
          data: responseCreateData,
        });
      }

      return assessment;
    });

    return NextResponse.json({ message: 'Progress saved successfully.', assessmentId: result.id, status: result.status }, { status: 200 });

  } catch (error) {
    console.error('Error saving assessment progress:', error);
    if (error instanceof SyntaxError) { // Handle JSON parsing errors specifically
        return NextResponse.json({ error: 'Invalid JSON in request body.' }, { status: 400 });
    }
    return NextResponse.json({ error: 'Failed to save assessment progress.' }, { status: 500 });
  }
}

export async function PUT(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session || !session.user || !session.user.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const body = await request.json() as AssessmentSubmitRequest;
    const { assessmentId, formData } = body;

    if (!assessmentId || !formData) {
      return NextResponse.json({ error: 'Invalid request body: assessmentId and formData are required.' }, { status: 400 });
    }

    const { sanitizedData, error } = validateFormData(formData);
    if (error) {
      return NextResponse.json({ error: error }, { status: 400 });
    }

    const userId = session.user.id;

    const result = await prisma.$transaction(async (tx) => {
      const assessment = await tx.assessment.findUnique({
        where: { id: assessmentId, userId: userId },
      });

      if (!assessment) {
        throw new Error('Assessment not found or user mismatch.'); // This will be caught and returned as 500 or specific error
      }

      if (assessment.status === AssessmentStatus.COMPLETED) {
        // Optionally allow re-submission or updates to completed assessments if needed
        // For now, let's assume completed assessments are final but can be re-saved with same data
        console.log(`Assessment ${assessmentId} is already completed. Re-saving final state.`);
      }

      const updatedAssessment = await tx.assessment.update({
        where: { id: assessmentId },
        data: {
          status: AssessmentStatus.COMPLETED,
          completedAt: new Date(),
          updatedAt: new Date(), // Also update updatedAt
        },
      });

      // Delete existing responses and recreate all based on final formData
      await tx.assessmentResponse.deleteMany({
        where: { assessmentId: assessmentId },
      });

      const responseCreateData = Object.entries(sanitizedData).map(([key, value]) => ({
        assessmentId: assessmentId,
        questionKey: key,
        answerValue: value === null || value === undefined ? Prisma.JsonNull : value,
      }));

      if (responseCreateData.length > 0) {
        await tx.assessmentResponse.createMany({
          data: responseCreateData,
        });
      }
      return updatedAssessment;
    });

    return NextResponse.json({ message: 'Assessment submitted successfully.', assessmentId: result.id, status: result.status }, { status: 200 });

  } catch (error) {
    console.error('Error submitting assessment:', error);
    if (error instanceof SyntaxError) {
      return NextResponse.json({ error: 'Invalid JSON in request body.' }, { status: 400 });
    }
    if (error instanceof Error && error.message === 'Assessment not found or user mismatch.') {
        return NextResponse.json({ error: error.message }, { status: 404 }); // Or 403 if preferred for mismatch
    }
    return NextResponse.json({ error: 'Failed to submit assessment.' }, { status: 500 });
  }
} 