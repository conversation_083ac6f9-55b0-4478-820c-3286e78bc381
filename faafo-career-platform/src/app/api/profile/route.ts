import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { ErrorReporter } from '@/lib/errorReporting';

// Helper function to calculate profile completion score
function calculateProfileCompletionScore(profileData: any): number {
  const fields = [
    'bio',
    'profilePictureUrl',
    'firstName',
    'lastName',
    'jobTitle',
    'company',
    'location',
    'careerInterests',
    'skillsToLearn',
    'experienceLevel',
    'currentIndustry',
    'targetIndustry'
  ];

  let completedFields = 0;

  fields.forEach(field => {
    const value = profileData[field];
    if (value !== null && value !== undefined && value !== '') {
      if (Array.isArray(value) && value.length > 0) {
        completedFields++;
      } else if (typeof value === 'string' && value.trim().length > 0) {
        completedFields++;
      } else if (typeof value !== 'string' && value) {
        completedFields++;
      }
    }
  });

  return Math.round((completedFields / fields.length) * 100);
}

export async function GET() {
  const session = await getServerSession(authOptions);

  if (!session?.user?.email) {
    return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
  }

  try {
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      include: { profile: true },
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // If profile doesn't exist, create an empty one
    let profile = user.profile;
    if (!profile) {
      profile = await prisma.profile.create({
        data: {
          userId: user.id,
        },
      });
    }

    return NextResponse.json(profile);
  } catch (error) {
    console.error('Error fetching profile:', error);

    // Report error to Sentry
    ErrorReporter.captureError(error as Error, {
      userId: session.user.email,
      userEmail: session.user.email,
      action: 'fetch_profile',
      component: 'profile_api',
    });

    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

export async function PUT(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session?.user?.email) {
    return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
  }

  try {
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const {
      bio,
      profilePictureUrl,
      socialMediaLinks,
      firstName,
      lastName,
      jobTitle,
      company,
      location,
      phoneNumber,
      website,
      careerInterests,
      skillsToLearn,
      experienceLevel,
      currentIndustry,
      targetIndustry,
      weeklyLearningGoal,
      emailNotifications,
      profileVisibility
    } = await request.json();

    // Calculate profile completion score
    const profileCompletionScore = calculateProfileCompletionScore({
      bio,
      profilePictureUrl,
      firstName,
      lastName,
      jobTitle,
      company,
      location,
      careerInterests,
      skillsToLearn,
      experienceLevel,
      currentIndustry,
      targetIndustry
    });

    const updatedProfile = await prisma.profile.upsert({
      where: { userId: user.id },
      update: {
        bio,
        profilePictureUrl,
        socialMediaLinks,
        currentCareerPath: careerInterests,
        progressLevel: experienceLevel,
        lastActiveAt: new Date(),
      },
      create: {
        userId: user.id,
        bio,
        profilePictureUrl,
        socialMediaLinks,
        currentCareerPath: careerInterests,
        progressLevel: experienceLevel,
      },
    });

    return NextResponse.json(updatedProfile);
  } catch (error) {
    console.error('Error updating profile:', error);

    // Report error to Sentry
    ErrorReporter.captureError(error as Error, {
      userId: session.user.email,
      userEmail: session.user.email,
      action: 'update_profile',
      component: 'profile_api',
    });

    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
} 