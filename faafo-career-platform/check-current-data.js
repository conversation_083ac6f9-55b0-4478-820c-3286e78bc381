const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkCurrentData() {
  try {
    console.log('🔍 Checking current database content...\n');

    // Check Users
    const users = await prisma.user.findMany({
      include: {
        profile: true,
        assessments: true,
        forumPosts: true,
        forumReplies: true
      }
    });
    console.log(`👥 Users: ${users.length}`);
    users.forEach(user => {
      console.log(`  - ${user.email} (ID: ${user.id})`);
      if (user.profile) {
        console.log(`    Profile: ${user.profile.bio || 'No bio'}`);
      }
      console.log(`    Assessments: ${user.assessments.length}`);
      console.log(`    Forum Posts: ${user.forumPosts.length}`);
      console.log(`    Forum Replies: ${user.forumReplies.length}`);
    });

    // Check Assessments
    const assessments = await prisma.assessment.findMany();
    console.log(`\n📝 Assessments: ${assessments.length}`);
    assessments.forEach(assessment => {
      console.log(`  - ${assessment.title} (Status: ${assessment.status})`);
    });

    // Check Assessment Responses
    const responses = await prisma.assessmentResponse.findMany();
    console.log(`\n💬 Assessment Responses: ${responses.length}`);

    // Check Career Paths
    const careerPaths = await prisma.careerPath.findMany();
    console.log(`\n🛤️ Career Paths: ${careerPaths.length}`);
    careerPaths.forEach(path => {
      console.log(`  - ${path.title}`);
    });

    // Check Skills
    const skills = await prisma.skill.findMany();
    console.log(`\n🎯 Skills: ${skills.length}`);

    // Check Learning Resources
    const resources = await prisma.learningResource.findMany();
    console.log(`\n📚 Learning Resources: ${resources.length}`);

    // Check Forum Posts
    const forumPosts = await prisma.forumPost.findMany({
      include: {
        author: true,
        replies: true
      }
    });
    console.log(`\n💬 Forum Posts: ${forumPosts.length}`);
    forumPosts.forEach(post => {
      console.log(`  - "${post.title}" by ${post.author.email} (${post.replies.length} replies)`);
    });

    // Check Forum Categories
    const forumCategories = await prisma.forumCategory.findMany();
    console.log(`\n📂 Forum Categories: ${forumCategories.length}`);

    // Check Learning Paths
    const learningPaths = await prisma.learningPath.findMany();
    console.log(`\n📖 Learning Paths: ${learningPaths.length}`);

    console.log('\n✅ Database content check complete!');

  } catch (error) {
    console.error('❌ Error checking database:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkCurrentData();
