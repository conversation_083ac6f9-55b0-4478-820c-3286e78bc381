/**
 * Integration tests for Navigation component
 * These tests would have caught the real issues we encountered
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { SessionProvider } from 'next-auth/react';
import { ThemeProvider } from 'next-themes';
import { NavigationBar } from '@/components/layout/NavigationBar';
import '@testing-library/jest-dom';

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    pathname: '/',
  }),
}));

// Mock next-auth
const mockSession = {
  user: {
    id: 'test-user-id',
    email: '<EMAIL>',
    name: 'Test User',
  },
  expires: '2024-12-31',
};

const MockSessionProvider = ({ children, session = null }: any) => (
  <SessionProvider session={session}>
    <ThemeProvider attribute="class" defaultTheme="light">
      {children}
    </ThemeProvider>
  </SessionProvider>
);

describe('NavigationBar Integration Tests', () => {
  describe('Icon Visibility Tests', () => {
    it('should render all navigation icons with proper visibility', () => {
      render(
        <MockSessionProvider session={mockSession}>
          <NavigationBar />
        </MockSessionProvider>
      );

      // Test that would have caught the icon visibility issue
      const homeIcon = screen.getByLabelText('Go to home page').querySelector('svg');
      const careerPathsIcon = screen.getByLabelText('Explore career paths').querySelector('svg');
      const resourcesIcon = screen.getByLabelText('Browse learning resources').querySelector('svg');
      const toolsIcon = screen.getByLabelText('Access career tools').querySelector('svg');
      const helpIcon = screen.getByLabelText('Get help and support').querySelector('svg');

      // Check that icons exist
      expect(homeIcon).toBeInTheDocument();
      expect(careerPathsIcon).toBeInTheDocument();
      expect(resourcesIcon).toBeInTheDocument();
      expect(toolsIcon).toBeInTheDocument();
      expect(helpIcon).toBeInTheDocument();

      // Check that icons have proper color classes (this would have caught the visibility issue)
      expect(homeIcon).toHaveClass('text-gray-600', 'dark:text-gray-300');
      expect(careerPathsIcon).toHaveClass('text-gray-600', 'dark:text-gray-300');
      expect(resourcesIcon).toHaveClass('text-gray-600', 'dark:text-gray-300');
      expect(toolsIcon).toHaveClass('text-gray-600', 'dark:text-gray-300');
      expect(helpIcon).toHaveClass('text-gray-600', 'dark:text-gray-300');
    });

    it('should render Tools icon specifically', () => {
      render(
        <MockSessionProvider session={mockSession}>
          <NavigationBar />
        </MockSessionProvider>
      );

      // This test would have caught the missing Tools icon
      const toolsLink = screen.getByLabelText('Access career tools');
      expect(toolsLink).toBeInTheDocument();
      expect(toolsLink).toHaveTextContent('Tools');
      
      const toolsIcon = toolsLink.querySelector('svg');
      expect(toolsIcon).toBeInTheDocument();
    });
  });

  describe('Authentication State Tests', () => {
    it('should show authenticated user navigation items', () => {
      render(
        <MockSessionProvider session={mockSession}>
          <NavigationBar />
        </MockSessionProvider>
      );

      // These should be visible for authenticated users
      expect(screen.getByLabelText('Visit community forum')).toBeInTheDocument();
      expect(screen.getByLabelText('View your profile')).toBeInTheDocument();
      expect(screen.getByLabelText('Sign out of your account')).toBeInTheDocument();

      // These should NOT be visible for authenticated users
      expect(screen.queryByLabelText('Log in to your account')).not.toBeInTheDocument();
      expect(screen.queryByLabelText('Create a new account')).not.toBeInTheDocument();
    });

    it('should show unauthenticated user navigation items', () => {
      render(
        <MockSessionProvider session={null}>
          <NavigationBar />
        </MockSessionProvider>
      );

      // These should be visible for unauthenticated users
      expect(screen.getByLabelText('Log in to your account')).toBeInTheDocument();
      expect(screen.getByLabelText('Create a new account')).toBeInTheDocument();

      // These should NOT be visible for unauthenticated users
      expect(screen.queryByLabelText('Visit community forum')).not.toBeInTheDocument();
      expect(screen.queryByLabelText('View your profile')).not.toBeInTheDocument();
      expect(screen.queryByLabelText('Sign out of your account')).not.toBeInTheDocument();
    });
  });

  describe('Mobile Navigation Tests', () => {
    it('should toggle mobile menu correctly', async () => {
      render(
        <MockSessionProvider session={mockSession}>
          <NavigationBar />
        </MockSessionProvider>
      );

      const mobileMenuButton = screen.getByLabelText('Toggle mobile menu');
      expect(mobileMenuButton).toBeInTheDocument();

      // Mobile menu should be closed initially
      // Check that mobile menu container is not visible
      expect(screen.queryByText('Light Mode')).not.toBeInTheDocument(); // Mobile-only theme toggle text

      // Open mobile menu
      fireEvent.click(mobileMenuButton);

      // Mobile menu items should be visible
      await waitFor(() => {
        expect(screen.getByText('Light Mode')).toBeInTheDocument(); // Mobile theme toggle
        expect(screen.getAllByText('Career Paths')).toHaveLength(2); // Desktop + mobile
        expect(screen.getAllByText('Resources')).toHaveLength(2); // Desktop + mobile
        expect(screen.getAllByText('Tools')).toHaveLength(2); // Desktop + mobile
        expect(screen.getAllByText('Help')).toHaveLength(2); // Desktop + mobile
      });
    });

    it('should have all icons in mobile menu', async () => {
      render(
        <MockSessionProvider session={mockSession}>
          <NavigationBar />
        </MockSessionProvider>
      );

      const mobileMenuButton = screen.getByLabelText('Toggle mobile menu');
      fireEvent.click(mobileMenuButton);

      await waitFor(() => {
        // Check that mobile menu has all the icons
        const mobileMenuItems = screen.getAllByRole('link');
        const mobileMenuButtons = screen.getAllByRole('button');
        
        // Should have icons for all navigation items
        expect(mobileMenuItems.length + mobileMenuButtons.length).toBeGreaterThan(5);
      });
    });
  });

  describe('Theme Toggle Tests', () => {
    it('should render theme toggle button with icon', () => {
      render(
        <MockSessionProvider session={mockSession}>
          <NavigationBar />
        </MockSessionProvider>
      );

      const themeButton = screen.getByLabelText(/Switch to .* theme/);
      expect(themeButton).toBeInTheDocument();
      
      const themeIcon = themeButton.querySelector('svg');
      expect(themeIcon).toBeInTheDocument();
      expect(themeIcon).toHaveClass('text-gray-600', 'dark:text-gray-300');
    });
  });

  describe('Accessibility Tests', () => {
    it('should have proper ARIA labels for all interactive elements', () => {
      render(
        <MockSessionProvider session={mockSession}>
          <NavigationBar />
        </MockSessionProvider>
      );

      // Check that all navigation items have proper ARIA labels
      expect(screen.getByLabelText('Go to home page')).toBeInTheDocument();
      expect(screen.getByLabelText('Explore career paths')).toBeInTheDocument();
      expect(screen.getByLabelText('Browse learning resources')).toBeInTheDocument();
      expect(screen.getByText('Tools')).toBeInTheDocument(); // Tools dropdown button
      expect(screen.getByLabelText('Get help and support')).toBeInTheDocument();
      expect(screen.getByLabelText('Visit community forum')).toBeInTheDocument();
      expect(screen.getByLabelText('View your profile')).toBeInTheDocument();
      expect(screen.getByLabelText('Sign out of your account')).toBeInTheDocument();
    });

    it('should have proper navigation landmark', () => {
      render(
        <MockSessionProvider session={mockSession}>
          <NavigationBar />
        </MockSessionProvider>
      );

      const nav = screen.getByRole('navigation', { name: 'Main navigation' });
      expect(nav).toBeInTheDocument();
    });
  });
});

// API Integration Tests
describe('Navigation API Integration', () => {
  // These tests would have caught the API 404 errors
  it('should handle profile API errors gracefully', async () => {
    // Mock fetch to return 404
    global.fetch = jest.fn(() =>
      Promise.resolve({
        ok: false,
        status: 404,
        json: () => Promise.resolve({ error: 'Not found' }),
      })
    ) as jest.Mock;

    render(
      <MockSessionProvider session={mockSession}>
        <NavigationBar />
      </MockSessionProvider>
    );

    // Navigation should still render even if profile API fails
    expect(screen.getByText('Profile')).toBeInTheDocument();
  });

  it('should handle forum API errors gracefully', async () => {
    // Mock fetch to return error
    global.fetch = jest.fn(() =>
      Promise.resolve({
        ok: false,
        status: 500,
        json: () => Promise.resolve({ error: 'Server error' }),
      })
    ) as jest.Mock;

    render(
      <MockSessionProvider session={mockSession}>
        <NavigationBar />
      </MockSessionProvider>
    );

    // Navigation should still render even if forum API fails
    expect(screen.getByText('Forum')).toBeInTheDocument();
  });
});

// Console Error Tests
describe('Console Error Detection', () => {
  let consoleSpy: jest.SpyInstance;

  beforeEach(() => {
    consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    consoleSpy.mockRestore();
  });

  it('should not produce console errors during render', () => {
    render(
      <MockSessionProvider session={mockSession}>
        <NavigationBar />
      </MockSessionProvider>
    );

    // This test would have caught the console errors we saw
    expect(consoleSpy).not.toHaveBeenCalled();
  });

  it('should not produce console errors during interactions', async () => {
    render(
      <MockSessionProvider session={mockSession}>
        <NavigationBar />
      </MockSessionProvider>
    );

    // Test mobile menu toggle
    const mobileMenuButton = screen.getByLabelText('Toggle mobile menu');
    fireEvent.click(mobileMenuButton);

    // Test theme toggle
    const themeButton = screen.getByLabelText(/Switch to .* theme/);
    fireEvent.click(themeButton);

    await waitFor(() => {
      expect(consoleSpy).not.toHaveBeenCalled();
    });
  });
});
