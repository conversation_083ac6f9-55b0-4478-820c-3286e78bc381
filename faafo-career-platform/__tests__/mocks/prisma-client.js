// Mock implementation of @prisma/client for Jest tests

const mockPrismaClient = {
  user: {
    findUnique: jest.fn(),
    findMany: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    count: jest.fn(),
  },
  assessment: {
    findFirst: jest.fn(),
    findMany: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    count: jest.fn(),
  },
  learningResource: {
    findMany: jest.fn(),
    findUnique: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    count: jest.fn(),
  },
  careerPath: {
    findMany: jest.fn(),
    findUnique: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    count: jest.fn(),
  },
  forumPost: {
    findMany: jest.fn(),
    findUnique: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    count: jest.fn(),
  },
  forumReply: {
    findMany: jest.fn(),
    create: jest.fn(),
    count: jest.fn(),
  },
  forumPostReaction: {
    findFirst: jest.fn(),
    create: jest.fn(),
    delete: jest.fn(),
    count: jest.fn(),
  },
  forumReplyReaction: {
    findFirst: jest.fn(),
    create: jest.fn(),
    delete: jest.fn(),
    count: jest.fn(),
  },
  forumBookmark: {
    findFirst: jest.fn(),
    create: jest.fn(),
    delete: jest.fn(),
    count: jest.fn(),
  },
  forumCategory: {
    findMany: jest.fn(),
    count: jest.fn(),
  },
  userGoal: {
    findMany: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    count: jest.fn(),
  },
  achievement: {
    findMany: jest.fn(),
    count: jest.fn(),
  },
  userAchievement: {
    findMany: jest.fn(),
    create: jest.fn(),
    count: jest.fn(),
  },
  userProgress: {
    findMany: jest.fn(),
    findUnique: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    count: jest.fn(),
  },
  resourceRating: {
    findMany: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    count: jest.fn(),
  },
  profile: {
    findUnique: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    count: jest.fn(),
  },
  $disconnect: jest.fn(),
  $executeRawUnsafe: jest.fn(),
};

// Mock Prisma error classes
class PrismaClientKnownRequestError extends Error {
  constructor(message, code, clientVersion) {
    super(message);
    this.code = code;
    this.clientVersion = clientVersion;
    this.name = 'PrismaClientKnownRequestError';
  }
}

class PrismaClientUnknownRequestError extends Error {
  constructor(message, clientVersion) {
    super(message);
    this.clientVersion = clientVersion;
    this.name = 'PrismaClientUnknownRequestError';
  }
}

class PrismaClientRustPanicError extends Error {
  constructor(message, clientVersion) {
    super(message);
    this.clientVersion = clientVersion;
    this.name = 'PrismaClientRustPanicError';
  }
}

class PrismaClientInitializationError extends Error {
  constructor(message, clientVersion) {
    super(message);
    this.clientVersion = clientVersion;
    this.name = 'PrismaClientInitializationError';
  }
}

class PrismaClientValidationError extends Error {
  constructor(message, clientVersion) {
    super(message);
    this.clientVersion = clientVersion;
    this.name = 'PrismaClientValidationError';
  }
}

// Export the mock
module.exports = {
  PrismaClient: jest.fn().mockImplementation(() => mockPrismaClient),
  Prisma: {
    PrismaClientKnownRequestError,
    PrismaClientUnknownRequestError,
    PrismaClientRustPanicError,
    PrismaClientInitializationError,
    PrismaClientValidationError,
  },
};
